const API_BASE_URL = import.meta.env.VITE_API_BACKEND_ENDPOINT;
import { getAuthHeaders } from './auth';
import { mockProperties } from '../data/mockProperties';

export const fetchProperties = async () => {
  console.log("Using local mock data for properties");
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    return mockProperties;
  } catch (error) {
    console.error('Error fetching properties:', error);
    throw error;
  }
};

export const fetchProperty = async (id) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 200));
    const property = mockProperties.find(p => p.id === parseInt(id));
    if (!property) {
      throw new Error(`Property with id ${id} not found`);
    }
    return property;
  } catch (error) {
    console.error('Error fetching property:', error);
    throw error;
  }
};

export const fetchPropertiesWithFilters = async (filters) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));

    let filteredProperties = [...mockProperties];

    // Apply price filters
    if (filters.priceRange.min > 0) {
      filteredProperties = filteredProperties.filter(p => p.price >= filters.priceRange.min);
    }
    if (filters.priceRange.max < 2000000) {
      filteredProperties = filteredProperties.filter(p => p.price <= filters.priceRange.max);
    }

    // Apply bedroom filter
    if (filters.bedrooms) {
      filteredProperties = filteredProperties.filter(p => p.bedrooms >= parseInt(filters.bedrooms));
    }

    // Apply bathroom filter
    if (filters.bathrooms) {
      filteredProperties = filteredProperties.filter(p => p.bathrooms >= parseInt(filters.bathrooms));
    }

    // Apply property type filter
    if (filters.propertyTypes.length > 0) {
      filteredProperties = filteredProperties.filter(p => filters.propertyTypes.includes(p.propertyType));
    }

    // Apply square footage filters
    if (filters.sqftRange.min > 0) {
      filteredProperties = filteredProperties.filter(p => p.squareFootage >= filters.sqftRange.min);
    }
    if (filters.sqftRange.max < 5000) {
      filteredProperties = filteredProperties.filter(p => p.squareFootage <= filters.sqftRange.max);
    }

    return filteredProperties;
  } catch (error) {
    console.error('Error fetching filtered properties:', error);
    throw error;
  }
};

export const saveProperty = async (propertyId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/saved-listings`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...getAuthHeaders()
      },
      body: JSON.stringify({ propertyId })
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to save property');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error saving property:', error);
    throw error;
  }
};

export const unsaveProperty = async (propertyId) => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/saved-listings/${propertyId}`, {
      method: 'DELETE',
      headers: getAuthHeaders()
    });
    
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to unsave property');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error unsaving property:', error);
    throw error;
  }
};

export const fetchSavedListings = async () => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/saved-listings`, {
      headers: getAuthHeaders()
    });
    
    if (!response.ok) {
      throw new Error(`Failed to fetch saved listings: ${response.status} ${response.statusText}`);
    }
    
    const data = await response.json();
    return data.savedListings || [];
  } catch (error) {
    console.error('Error fetching saved listings:', error);
    throw error;
  }
};
